#!/usr/bin/env python3
"""
Script para configurar la base de datos PostgreSQL con el schema
"""
import asyncio
import asyncpg
import sys
from pathlib import Path

async def setup_database(db_url: str):
    """Configurar la base de datos con el schema SQL"""
    
    print("🔧 Configurando base de datos...")
    
    try:
        # Conectar a la base de datos
        conn = await asyncpg.connect(db_url)
        print("✅ Conexión exitosa a PostgreSQL")
        
        # Leer el archivo schema.sql
        schema_path = Path("sql/schema.sql")
        if not schema_path.exists():
            print("❌ No se encontró el archivo sql/schema.sql")
            return False
            
        schema_sql = schema_path.read_text(encoding='utf-8')
        print("📄 Archivo schema.sql leído correctamente")
        
        # Ejecutar el schema SQL
        await conn.execute(schema_sql)
        print("✅ Schema ejecutado correctamente")
        
        # Verificar que las tablas se crearon
        tables = await conn.fetch("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name IN ('documents', 'chunks')
        """)
        
        print(f"📋 Tablas creadas: {[table['table_name'] for table in tables]}")
        
        # Verificar que PGVector esté disponible
        extensions = await conn.fetch("SELECT * FROM pg_extension WHERE extname = 'vector'")
        if extensions:
            print("✅ Extensión PGVector está disponible")
        else:
            print("⚠️  Extensión PGVector no encontrada, intentando instalar...")
            try:
                await conn.execute("CREATE EXTENSION IF NOT EXISTS vector")
                print("✅ Extensión PGVector instalada")
            except Exception as e:
                print(f"❌ Error instalando PGVector: {e}")
                return False
        
        # Verificar función match_chunks
        functions = await conn.fetch("""
            SELECT routine_name 
            FROM information_schema.routines 
            WHERE routine_schema = 'public' 
            AND routine_name = 'match_chunks'
        """)
        
        if functions:
            print("✅ Función match_chunks creada correctamente")
        else:
            print("⚠️  Función match_chunks no encontrada")
        
        await conn.close()
        print("🎉 ¡Base de datos configurada correctamente!")
        return True
        
    except Exception as e:
        print(f"❌ Error configurando la base de datos: {e}")
        return False

async def main():
    """Función principal"""
    
    # URLs de prueba comunes
    test_urls = [
        "postgresql://postgres@localhost:5432/postgres",
        "postgresql://postgres:postgres@localhost:5432/postgres", 
        "postgresql://postgres:admin@localhost:5432/postgres",
        "postgresql://admin@localhost:5432/postgres",
        "postgresql://admin:admin@localhost:5432/postgres",
    ]
    
    print("🔍 Buscando conexión válida a PostgreSQL...")
    print("=" * 50)
    
    working_url = None
    
    for i, db_url in enumerate(test_urls, 1):
        print(f"\n{i}. Probando: {db_url}")
        try:
            conn = await asyncpg.connect(db_url)
            await conn.close()
            print("   ✅ Conexión exitosa!")
            working_url = db_url
            break
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
    
    if not working_url:
        print("\n❌ No se pudo conectar con ninguna configuración")
        print("💡 Verifica que PostgreSQL esté corriendo y las credenciales sean correctas")
        return
    
    print(f"\n🎯 Usando URL: {working_url}")
    
    # Configurar la base de datos
    success = await setup_database(working_url)
    
    if success:
        print(f"\n🎉 ¡Configuración completada!")
        print(f"📋 Agrega esta línea a tu archivo .env:")
        print(f"DATABASE_URL={working_url}")
    else:
        print(f"\n❌ Error en la configuración")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Cancelado por el usuario")
        sys.exit(1)
