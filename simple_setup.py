#!/usr/bin/env python3
"""
Script simple para configurar la base de datos usando la URL del .env
"""
import asyncio
import asyncpg
import os
from pathlib import Path
from dotenv import load_dotenv

# Cargar variables de entorno
load_dotenv()

async def setup_database():
    """Configurar la base de datos con el schema SQL"""
    
    database_url = os.getenv('DATABASE_URL')
    
    if not database_url:
        print("❌ No se encontró DATABASE_URL en el archivo .env")
        return False
    
    print(f"🔗 Conectando a: {database_url}")
    
    try:
        # Conectar a la base de datos
        conn = await asyncpg.connect(database_url)
        print("✅ Conexión exitosa")
        
        # Leer el archivo schema.sql
        schema_path = Path("sql/schema.sql")
        if not schema_path.exists():
            print("❌ No se encontró el archivo sql/schema.sql")
            return False
            
        schema_sql = schema_path.read_text(encoding='utf-8')
        print("📄 Archivo schema.sql leído")
        
        # Ejecutar el schema SQL
        await conn.execute(schema_sql)
        print("✅ Schema ejecutado correctamente")
        
        # Verificar que las tablas se crearon
        tables = await conn.fetch("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name IN ('documents', 'chunks')
        """)
        
        print(f"📋 Tablas creadas: {[table['table_name'] for table in tables]}")
        
        # Verificar PGVector
        try:
            await conn.execute("CREATE EXTENSION IF NOT EXISTS vector")
            print("✅ Extensión PGVector disponible")
        except Exception as e:
            print(f"⚠️  Error con PGVector: {e}")
        
        await conn.close()
        print("🎉 ¡Base de datos configurada correctamente!")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(setup_database())
