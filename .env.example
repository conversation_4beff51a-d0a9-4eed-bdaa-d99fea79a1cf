# Database Configuration
# PostgreSQL with PGVector extension
DATABASE_URL=postgresql://raguser:ragpass123@localhost:5432/postgres

# OpenAI Configuration (Required)
# Get from https://platform.openai.com/api-keys
OPENAI_API_KEY=your-openai-api-key

# Model Selection
# Choose which OpenAI model to use for both ingestion and agent
LLM_CHOICE=gpt-4o-mini

# Embedding Model
EMBEDDING_MODEL=text-embedding-3-small

# Development Settings
LOG_LEVEL=INFO
DEBUG_MODE=false