#!/usr/bin/env python3
"""
Script para diagnosticar problemas de conexión PostgreSQL
"""
import asyncio
import asyncpg
import socket
import subprocess
import sys

def check_port(host, port):
    """Verificar si un puerto está abierto"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except:
        return False

def check_postgres_service():
    """Verificar el estado del servicio PostgreSQL"""
    try:
        result = subprocess.run(
            ['powershell', '-Command', 'Get-Service -Name "*postgres*" | Format-Table -AutoSize'],
            capture_output=True,
            text=True,
            timeout=10
        )
        return result.stdout
    except:
        return "No se pudo verificar el servicio"

async def test_connection_advanced(host, port, user, password, database):
    """Probar conexión con parámetros específicos"""
    try:
        if password:
            dsn = f"postgresql://{user}:{password}@{host}:{port}/{database}"
        else:
            dsn = f"postgresql://{user}@{host}:{port}/{database}"
        
        conn = await asyncpg.connect(dsn, timeout=5)
        version = await conn.fetchval("SELECT version()")
        await conn.close()
        return True, version
    except Exception as e:
        return False, str(e)

async def main():
    """Diagnóstico completo"""
    
    print("🔍 Diagnóstico completo de PostgreSQL")
    print("=" * 50)
    
    # 1. Verificar servicio
    print("\n1️⃣ Estado del servicio PostgreSQL:")
    service_status = check_postgres_service()
    print(service_status)
    
    # 2. Verificar puertos
    print("\n2️⃣ Verificando puertos:")
    common_ports = [5432, 5433, 5434]
    open_ports = []
    
    for port in common_ports:
        is_open = check_port('localhost', port)
        status = "✅ ABIERTO" if is_open else "❌ CERRADO"
        print(f"   Puerto {port}: {status}")
        if is_open:
            open_ports.append(port)
    
    if not open_ports:
        print("\n❌ No hay puertos PostgreSQL abiertos")
        print("💡 Soluciones:")
        print("   1. Reinicia el servicio: Restart-Service postgresql-x64-18")
        print("   2. Verifica la configuración en postgresql.conf")
        print("   3. Verifica que listen_addresses = '*' o 'localhost'")
        return
    
    # 3. Probar conexiones en puertos abiertos
    print(f"\n3️⃣ Probando conexiones en puertos abiertos: {open_ports}")
    
    # Configuraciones de prueba
    test_configs = [
        ('postgres', None),
        ('postgres', ''),
        ('postgres', 'postgres'),
        ('postgres', 'admin'),
        ('admin', None),
        ('admin', 'admin'),
    ]
    
    databases = ['postgres', 'template1']
    
    working_configs = []
    
    for port in open_ports:
        print(f"\n🔌 Puerto {port}:")
        
        for db in databases:
            for user, password in test_configs:
                config_str = f"{user}:{password or '(sin contraseña)'} -> {db}"
                print(f"   Probando {config_str}...")
                
                success, result = await test_connection_advanced('localhost', port, user, password, db)
                
                if success:
                    print(f"   ✅ ¡ÉXITO! {config_str}")
                    if password:
                        dsn = f"postgresql://{user}:{password}@localhost:{port}/{db}"
                    else:
                        dsn = f"postgresql://{user}@localhost:{port}/{db}"
                    working_configs.append((dsn, result))
                    break
                else:
                    print(f"   ❌ Error: {result[:50]}...")
            
            if working_configs:
                break
        
        if working_configs:
            break
    
    # 4. Mostrar resultados
    if working_configs:
        print(f"\n🎉 ¡Conexiones exitosas encontradas!")
        print("=" * 50)
        
        for i, (dsn, version) in enumerate(working_configs, 1):
            print(f"\n{i}. URL: {dsn}")
            print(f"   Versión: {version[:80]}...")
        
        print(f"\n📋 Agrega esta línea a tu archivo .env:")
        print(f"DATABASE_URL={working_configs[0][0]}")
        
    else:
        print(f"\n❌ No se encontraron conexiones válidas")
        print(f"\n🔧 Próximos pasos:")
        print(f"   1. Verifica la contraseña del usuario postgres")
        print(f"   2. Revisa pg_hba.conf para configuración de autenticación")
        print(f"   3. Considera reinstalar PostgreSQL si es necesario")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Cancelado por el usuario")
        sys.exit(1)
