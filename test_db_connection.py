#!/usr/bin/env python3
"""
Script para probar la conexión a PostgreSQL
"""
import asyncio
import asyncpg
import sys

async def test_connection():
    """Probar diferentes configuraciones de conexión a PostgreSQL"""
    
    # Configuraciones comunes para PostgreSQL local
    test_configs = [
        "postgresql://postgres@localhost:5432/postgres",
        "postgresql://postgres:postgres@localhost:5432/postgres", 
        "postgresql://postgres:admin@localhost:5432/postgres",
        "postgresql://admin@localhost:5432/postgres",
        "postgresql://admin:admin@localhost:5432/postgres",
    ]
    
    print("🔍 Probando conexiones a PostgreSQL local...")
    print("=" * 50)
    
    for i, db_url in enumerate(test_configs, 1):
        print(f"\n{i}. Probando: {db_url}")
        try:
            conn = await asyncpg.connect(db_url)
            
            # Verificar que PGVector esté disponible
            result = await conn.fetch("SELECT * FROM pg_extension WHERE extname = 'vector'")
            
            if result:
                print("   ✅ Conexión exitosa!")
                print("   ✅ PGVector está instalado")
                print(f"   📋 URL a usar: {db_url}")
                await conn.close()
                return db_url
            else:
                print("   ✅ Conexión exitosa!")
                print("   ⚠️  PGVector NO está instalado")
                print("   💡 Ejecuta: CREATE EXTENSION IF NOT EXISTS vector;")
                await conn.close()
                return db_url
                
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
    
    print("\n❌ No se pudo conectar con ninguna configuración")
    print("\n💡 Sugerencias:")
    print("   1. Verifica que PostgreSQL esté corriendo")
    print("   2. Verifica usuario y contraseña")
    print("   3. Verifica que el puerto 5432 esté abierto")
    return None

if __name__ == "__main__":
    try:
        result = asyncio.run(test_connection())
        if result:
            print(f"\n🎉 ¡Éxito! Usa esta URL en tu .env:")
            print(f"DATABASE_URL={result}")
    except KeyboardInterrupt:
        print("\n👋 Cancelado por el usuario")
        sys.exit(1)
